#!/usr/bin/env python3
"""
Comprehensive Test Suite for All 75 Masonite Backend Endpoints
Tests every single endpoint with proper authentication, validation, and error handling
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# Configuration
BASE_URL = "http://localhost:8001/api"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "TestPassword123!"
TEST_USER_FIRST_NAME = "Comprehensive"
TEST_USER_LAST_NAME = "Test"

class ComprehensiveEndpointTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.session = requests.Session()
        self.auth_token = None
        self.test_user_id = None
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    def log_test(self, endpoint: str, method: str, status: str, expected_code: int, actual_code: int, message: str = ""):
        """Log test result"""
        self.total_tests += 1
        if status == "PASS":
            self.passed_tests += 1
            print(f"✅ {method} {endpoint} - {status} ({actual_code}) {message}")
        else:
            self.failed_tests += 1
            print(f"❌ {method} {endpoint} - {status} (Expected: {expected_code}, Got: {actual_code}) {message}")
        
        self.test_results.append({
            'endpoint': endpoint,
            'method': method,
            'status': status,
            'expected_code': expected_code,
            'actual_code': actual_code,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })

    def make_request(self, method: str, endpoint: str, data: Dict = None, headers: Dict = None, auth_required: bool = True) -> Tuple[int, Dict]:
        """Make HTTP request with proper headers"""
        url = f"{self.base_url}{endpoint}"
        
        # Prepare headers
        request_headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        if auth_required and self.auth_token:
            request_headers['Authorization'] = f'Bearer {self.auth_token}'
            
        if headers:
            request_headers.update(headers)
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, headers=request_headers, timeout=30)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, headers=request_headers, timeout=30)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data, headers=request_headers, timeout=30)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, json=data, headers=request_headers, timeout=30)
            elif method.upper() == 'OPTIONS':
                response = self.session.options(url, headers=request_headers, timeout=30)
            else:
                return 500, {'error': f'Unsupported method: {method}'}
            
            try:
                return response.status_code, response.json()
            except:
                return response.status_code, {'raw_response': response.text}
                
        except requests.exceptions.RequestException as e:
            return 500, {'error': str(e)}

    def setup_test_user(self) -> bool:
        """Create test user and get authentication token"""
        print("\n🔧 Setting up test user...")
        
        # Register test user
        register_data = {
            'firstName': TEST_USER_FIRST_NAME,
            'lastName': TEST_USER_LAST_NAME,
            'email': TEST_USER_EMAIL,
            'password': TEST_USER_PASSWORD,
            'password_confirmation': TEST_USER_PASSWORD
        }
        
        status_code, response = self.make_request('POST', '/auth/register', register_data, auth_required=False)
        
        if status_code in [200, 201]:
            self.auth_token = response.get('token')
            user_data = response.get('user', {})
            self.test_user_id = user_data.get('id')
            print(f"✅ Test user created successfully - ID: {self.test_user_id}")
            return True
        elif status_code == 422 and 'already exists' in str(response):
            # User already exists, try to login
            login_data = {
                'email': TEST_USER_EMAIL,
                'password': TEST_USER_PASSWORD
            }
            status_code, response = self.make_request('POST', '/auth/login', login_data, auth_required=False)
            if status_code == 200:
                self.auth_token = response.get('token')
                user_data = response.get('user', {})
                self.test_user_id = user_data.get('id')
                print(f"✅ Logged in with existing test user - ID: {self.test_user_id}")
                return True
        
        print(f"❌ Failed to setup test user: {status_code} - {response}")
        return False

    def test_cors_endpoints(self):
        """Test CORS endpoints (1 endpoint)"""
        print("\n🌐 Testing CORS Endpoints...")
        
        # OPTIONS /* - CORS preflight
        status_code, response = self.make_request('OPTIONS', '/auth/login', auth_required=False)
        expected = [200, 204]
        if status_code in expected:
            self.log_test('/cors/preflight', 'OPTIONS', 'PASS', 200, status_code)
        else:
            self.log_test('/cors/preflight', 'OPTIONS', 'FAIL', 200, status_code, str(response))

    def test_authentication_endpoints(self):
        """Test Authentication endpoints (8 endpoints)"""
        print("\n🔐 Testing Authentication Endpoints...")

        # POST /auth/login
        login_data = {'email': TEST_USER_EMAIL, 'password': TEST_USER_PASSWORD}
        status_code, response = self.make_request('POST', '/auth/login', login_data, auth_required=False)
        if status_code == 200 and 'token' in response:
            self.log_test('/auth/login', 'POST', 'PASS', 200, status_code)
            # Update our token to ensure we have the latest one
            self.auth_token = response['token']
        else:
            self.log_test('/auth/login', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /auth/register (with different email)
        register_data = {
            'firstName': 'Test2',
            'lastName': 'User2',
            'email': f'test2_{int(time.time())}@example.com',
            'password': TEST_USER_PASSWORD,
            'password_confirmation': TEST_USER_PASSWORD
        }
        status_code, response = self.make_request('POST', '/auth/register', register_data, auth_required=False)
        if status_code in [200, 201]:
            self.log_test('/auth/register', 'POST', 'PASS', 201, status_code)
            # Update token if we got a new one
            if 'token' in response:
                self.auth_token = response['token']
        else:
            self.log_test('/auth/register', 'POST', 'FAIL', 201, status_code, str(response))

        # GET /auth/profile
        status_code, response = self.make_request('GET', '/auth/profile')
        if status_code == 200 and 'user' in response:
            self.log_test('/auth/profile', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/auth/profile', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /auth/refresh
        status_code, response = self.make_request('POST', '/auth/refresh')
        if status_code == 200:
            self.log_test('/auth/refresh', 'POST', 'PASS', 200, status_code)
            # Update token after refresh
            if 'token' in response:
                self.auth_token = response['token']
        else:
            self.log_test('/auth/refresh', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /auth/verify-email (expect validation error)
        verify_data = {'token': 'invalid_token'}
        status_code, response = self.make_request('POST', '/auth/verify-email', verify_data, auth_required=False)
        if status_code in [400, 422]:
            self.log_test('/auth/verify-email', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/auth/verify-email', 'POST', 'FAIL', 400, status_code, str(response))

        # POST /auth/forgot-password
        forgot_data = {'email': TEST_USER_EMAIL}
        status_code, response = self.make_request('POST', '/auth/forgot-password', forgot_data, auth_required=False)
        if status_code == 200:
            self.log_test('/auth/forgot-password', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/auth/forgot-password', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /auth/reset-password (expect validation error)
        reset_data = {'token': 'invalid', 'password': 'newpass', 'password_confirmation': 'newpass'}
        status_code, response = self.make_request('POST', '/auth/reset-password', reset_data, auth_required=False)
        if status_code in [400, 422]:
            self.log_test('/auth/reset-password', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/auth/reset-password', 'POST', 'FAIL', 400, status_code, str(response))

        # POST /auth/logout
        status_code, response = self.make_request('POST', '/auth/logout')
        if status_code == 200:
            self.log_test('/auth/logout', 'POST', 'PASS', 200, status_code)
            # Re-login for subsequent tests
            self.setup_test_user()
        else:
            self.log_test('/auth/logout', 'POST', 'FAIL', 200, status_code, str(response))

    def test_basic_endpoints(self):
        """Test basic endpoints first"""
        print("\n🧪 Testing Basic Endpoints...")
        
        # Test server connectivity
        try:
            response = requests.get("http://localhost:8001", timeout=5)
            print(f"✅ Server is running - Status: {response.status_code}")
        except:
            print("❌ Server is not running or not accessible")
            return False
        
        # Test a simple endpoint
        status_code, response = self.make_request('GET', '/payments/test', auth_required=False)
        if status_code == 200:
            print("✅ Basic API endpoint working")
            return True
        else:
            print(f"❌ Basic API endpoint failed: {status_code}")
            return False

    def test_two_factor_endpoints(self):
        """Test Two-Factor Authentication endpoints (5 endpoints)"""
        print("\n🔒 Testing Two-Factor Authentication Endpoints...")

        # POST /two-factor/setup
        status_code, response = self.make_request('POST', '/two-factor/setup')
        if status_code == 200:
            self.log_test('/two-factor/setup', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/two-factor/setup', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /two-factor/verify (expect validation error)
        verify_data = {'token': '123456'}
        status_code, response = self.make_request('POST', '/two-factor/verify', verify_data)
        if status_code in [400, 422]:
            self.log_test('/two-factor/verify', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/two-factor/verify', 'POST', 'FAIL', 400, status_code, str(response))

        # GET /two-factor/recovery-codes
        status_code, response = self.make_request('GET', '/two-factor/recovery-codes')
        if status_code == 200:
            self.log_test('/two-factor/recovery-codes', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/two-factor/recovery-codes', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /two-factor/regenerate-codes (expect validation error without password)
        status_code, response = self.make_request('POST', '/two-factor/regenerate-codes', {})
        if status_code in [400, 422]:
            self.log_test('/two-factor/regenerate-codes', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/two-factor/regenerate-codes', 'POST', 'FAIL', 400, status_code, str(response))

        # POST /two-factor/disable (expect validation error)
        disable_data = {'password': 'wrongpassword'}
        status_code, response = self.make_request('POST', '/two-factor/disable', disable_data)
        if status_code in [400, 422]:
            self.log_test('/two-factor/disable', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/two-factor/disable', 'POST', 'FAIL', 400, status_code, str(response))

    def test_oauth_endpoints(self):
        """Test OAuth endpoints (5 endpoints)"""
        print("\n🔗 Testing OAuth Endpoints...")

        # GET /oauth/providers
        status_code, response = self.make_request('GET', '/oauth/providers', auth_required=False)
        if status_code == 200:
            self.log_test('/oauth/providers', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/oauth/providers', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /oauth/google/url
        status_code, response = self.make_request('GET', '/oauth/google/url', auth_required=False)
        if status_code == 200:
            self.log_test('/oauth/google/url', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/oauth/google/url', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /oauth/github/url
        status_code, response = self.make_request('GET', '/oauth/github/url', auth_required=False)
        if status_code == 200:
            self.log_test('/oauth/github/url', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/oauth/github/url', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /oauth/exchange-token (expect validation error)
        exchange_data = {'code': 'invalid_code'}
        status_code, response = self.make_request('POST', '/oauth/exchange-token', exchange_data, auth_required=False)
        if status_code in [400, 422]:
            self.log_test('/oauth/exchange-token', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/oauth/exchange-token', 'POST', 'FAIL', 400, status_code, str(response))

        # GET /oauth/callback (expect redirect or error)
        status_code, response = self.make_request('GET', '/oauth/callback', auth_required=False)
        if status_code in [200, 302, 400]:
            self.log_test('/oauth/callback', 'GET', 'PASS', 200, status_code, 'Expected response')
        else:
            self.log_test('/oauth/callback', 'GET', 'FAIL', 200, status_code, str(response))

    def test_payment_endpoints(self):
        """Test Payment endpoints (10 endpoints)"""
        print("\n💳 Testing Payment Endpoints...")

        # GET /payments/test
        status_code, response = self.make_request('GET', '/payments/test', auth_required=False)
        if status_code == 200:
            self.log_test('/payments/test', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/payments/test', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /payments/create-order
        order_data = {'amount': 100.50, 'currency': 'INR', 'description': 'Test payment'}
        status_code, response = self.make_request('POST', '/payments/create-order', order_data)
        test_order_id = None
        if status_code == 200 and 'orderId' in response:
            test_order_id = response['orderId']
            self.log_test('/payments/create-order', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/payments/create-order', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /payments/verify (expect validation error)
        verify_data = {'orderId': 'invalid', 'paymentId': 'invalid', 'signature': 'invalid'}
        status_code, response = self.make_request('POST', '/payments/verify', verify_data)
        if status_code in [400, 422]:
            self.log_test('/payments/verify', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/payments/verify', 'POST', 'FAIL', 400, status_code, str(response))

        # GET /payments/user
        status_code, response = self.make_request('GET', '/payments/user')
        if status_code == 200:
            self.log_test('/payments/user', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/payments/user', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /payments/analytics
        status_code, response = self.make_request('GET', '/payments/analytics')
        if status_code == 200:
            self.log_test('/payments/analytics', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/payments/analytics', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /payments/refunds
        status_code, response = self.make_request('GET', '/payments/refunds')
        if status_code == 200:
            self.log_test('/payments/refunds', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/payments/refunds', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /payments/refund (expect validation error)
        refund_data = {'paymentId': 'invalid_payment_id', 'reason': 'Test refund'}
        status_code, response = self.make_request('POST', '/payments/refund', refund_data)
        if status_code in [400, 404, 422]:
            self.log_test('/payments/refund', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/payments/refund', 'POST', 'FAIL', 400, status_code, str(response))

        # POST /payments/cancel (expect validation error)
        cancel_data = {'orderId': 'invalid_order_id'}
        status_code, response = self.make_request('POST', '/payments/cancel', cancel_data)
        if status_code in [400, 404, 422]:
            self.log_test('/payments/cancel', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/payments/cancel', 'POST', 'FAIL', 400, status_code, str(response))

        # GET /payments/status/{order_id} (if we have a test order)
        if test_order_id:
            status_code, response = self.make_request('GET', f'/payments/status/{test_order_id}')
            if status_code in [200, 404]:
                self.log_test('/payments/status/{order_id}', 'GET', 'PASS', 200, status_code)
            else:
                self.log_test('/payments/status/{order_id}', 'GET', 'FAIL', 200, status_code, str(response))
        else:
            self.log_test('/payments/status/{order_id}', 'GET', 'SKIP', 200, 0, 'No test order ID available')

        # POST /payments/webhook (expect validation error)
        webhook_data = {'event': 'test', 'payload': {}}
        status_code, response = self.make_request('POST', '/payments/webhook', webhook_data, auth_required=False)
        if status_code in [400, 422]:
            self.log_test('/payments/webhook', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/payments/webhook', 'POST', 'FAIL', 400, status_code, str(response))

    def test_account_management_endpoints(self):
        """Test Account Management endpoints (10 endpoints)"""
        print("\n👤 Testing Account Management Endpoints...")

        # Authenticated endpoints
        # POST /account/request-deletion
        deletion_data = {
            'preservePaymentData': True,
            'preserveTransactionHistory': True,
            'reason': 'Test deletion request'
        }
        status_code, response = self.make_request('POST', '/account/request-deletion', deletion_data)
        if status_code == 200:
            self.log_test('/account/request-deletion', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/account/request-deletion', 'POST', 'FAIL', 200, status_code, str(response))

        # GET /account/deletion-status
        status_code, response = self.make_request('GET', '/account/deletion-status')
        if status_code == 200:
            self.log_test('/account/deletion-status', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/account/deletion-status', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /account/cancel-deletion
        status_code, response = self.make_request('POST', '/account/cancel-deletion', {})
        if status_code in [200, 400]:  # May not have pending deletion
            self.log_test('/account/cancel-deletion', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/account/cancel-deletion', 'POST', 'FAIL', 200, status_code, str(response))

        # GET /account/export-data
        status_code, response = self.make_request('GET', '/account/export-data')
        if status_code == 200:
            self.log_test('/account/export-data', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/account/export-data', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /account/request-export
        status_code, response = self.make_request('POST', '/account/request-export', {})
        if status_code == 200:
            self.log_test('/account/request-export', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/account/request-export', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /account/cleanup-expired (admin endpoint)
        status_code, response = self.make_request('POST', '/account/cleanup-expired', {}, auth_required=False)
        if status_code == 200:
            self.log_test('/account/cleanup-expired', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/account/cleanup-expired', 'POST', 'FAIL', 200, status_code, str(response))

        # Public endpoints
        # POST /account/confirm-deletion (expect validation error)
        confirm_data = {'token': 'invalid_token'}
        status_code, response = self.make_request('POST', '/account/confirm-deletion', confirm_data, auth_required=False)
        if status_code in [400, 422]:
            self.log_test('/account/confirm-deletion', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/account/confirm-deletion', 'POST', 'FAIL', 400, status_code, str(response))

        # POST /account/check-preserved-data
        check_data = {'email': TEST_USER_EMAIL}
        status_code, response = self.make_request('POST', '/account/check-preserved-data', check_data, auth_required=False)
        if status_code == 200:
            self.log_test('/account/check-preserved-data', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/account/check-preserved-data', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /account/restore-data (expect validation error)
        restore_data = {'userId': 'invalid', 'email': TEST_USER_EMAIL}
        status_code, response = self.make_request('POST', '/account/restore-data', restore_data, auth_required=False)
        if status_code in [400, 422]:
            self.log_test('/account/restore-data', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/account/restore-data', 'POST', 'FAIL', 400, status_code, str(response))

        # DELETE /account/delete-preserved-data
        delete_data = {'email': TEST_USER_EMAIL}
        status_code, response = self.make_request('DELETE', '/account/delete-preserved-data', delete_data, auth_required=False)
        if status_code in [200, 404]:
            self.log_test('/account/delete-preserved-data', 'DELETE', 'PASS', 200, status_code)
        else:
            self.log_test('/account/delete-preserved-data', 'DELETE', 'FAIL', 200, status_code, str(response))

    def test_otp_endpoints(self):
        """Test OTP endpoints (7 endpoints)"""
        print("\n📱 Testing OTP Endpoints...")

        # POST /otp/send
        otp_data = {'email': TEST_USER_EMAIL, 'type': 'login'}
        status_code, response = self.make_request('POST', '/otp/send', otp_data, auth_required=False)
        if status_code == 200:
            self.log_test('/otp/send', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/otp/send', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /otp/send-email
        status_code, response = self.make_request('POST', '/otp/send-email', otp_data, auth_required=False)
        if status_code == 200:
            self.log_test('/otp/send-email', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/otp/send-email', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /otp/send-sms
        sms_data = {'phone': '+1234567890', 'type': 'login'}
        status_code, response = self.make_request('POST', '/otp/send-sms', sms_data, auth_required=False)
        if status_code in [200, 400]:  # May not have SMS configured
            self.log_test('/otp/send-sms', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/otp/send-sms', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /otp/verify (expect validation error or proper response)
        verify_data = {'email': TEST_USER_EMAIL, 'otp': '123456'}
        status_code, response = self.make_request('POST', '/otp/verify', verify_data, auth_required=False)
        if status_code in [200, 400, 422]:  # 200 is valid for "invalid OTP" response
            self.log_test('/otp/verify', 'POST', 'PASS', 200, status_code, 'Expected response')
        else:
            self.log_test('/otp/verify', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /otp/login (expect validation error or unauthorized)
        login_data = {'email': TEST_USER_EMAIL, 'otp': '123456'}
        status_code, response = self.make_request('POST', '/otp/login', login_data, auth_required=False)
        if status_code in [400, 401, 422]:  # 401 is valid for "no valid OTP"
            self.log_test('/otp/login', 'POST', 'PASS', 400, status_code, 'Expected error response')
        else:
            self.log_test('/otp/login', 'POST', 'FAIL', 400, status_code, str(response))

        # GET /otp/status
        status_code, response = self.make_request('GET', '/otp/status?email=' + TEST_USER_EMAIL, auth_required=False)
        if status_code == 200:
            self.log_test('/otp/status', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/otp/status', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /otp/cleanup (admin endpoint)
        status_code, response = self.make_request('POST', '/otp/cleanup', {}, auth_required=False)
        if status_code == 200:
            self.log_test('/otp/cleanup', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/otp/cleanup', 'POST', 'FAIL', 200, status_code, str(response))

    def test_security_endpoints(self):
        """Test Security endpoints (10 endpoints)"""
        print("\n🛡️ Testing Security Endpoints...")

        # GET /security/dashboard
        status_code, response = self.make_request('GET', '/security/dashboard')
        if status_code == 200:
            self.log_test('/security/dashboard', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/security/dashboard', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /security/events
        status_code, response = self.make_request('GET', '/security/events')
        if status_code == 200:
            self.log_test('/security/events', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/security/events', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /security/events/user
        status_code, response = self.make_request('GET', '/security/events/user')
        if status_code == 200:
            self.log_test('/security/events/user', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/security/events/user', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /security/suspicious-activity
        status_code, response = self.make_request('GET', '/security/suspicious-activity')
        if status_code == 200:
            self.log_test('/security/suspicious-activity', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/security/suspicious-activity', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /security/analysis
        status_code, response = self.make_request('GET', '/security/analysis')
        if status_code == 200:
            self.log_test('/security/analysis', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/security/analysis', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /security/statistics
        status_code, response = self.make_request('GET', '/security/statistics')
        if status_code == 200:
            self.log_test('/security/statistics', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/security/statistics', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /security/account-status
        status_code, response = self.make_request('GET', '/security/account-status')
        if status_code == 200:
            self.log_test('/security/account-status', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/security/account-status', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /security/unlock-account (expect validation error)
        unlock_data = {'userId': 'invalid_user_id'}
        status_code, response = self.make_request('POST', '/security/unlock-account', unlock_data)
        if status_code in [400, 404, 422]:
            self.log_test('/security/unlock-account', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/security/unlock-account', 'POST', 'FAIL', 400, status_code, str(response))

        # POST /security/cleanup
        status_code, response = self.make_request('POST', '/security/cleanup', {})
        if status_code == 200:
            self.log_test('/security/cleanup', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/security/cleanup', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /security/events/{event_id}/resolve (expect validation error)
        status_code, response = self.make_request('POST', '/security/events/invalid_id/resolve', {})
        if status_code in [400, 404, 422]:
            self.log_test('/security/events/{event_id}/resolve', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/security/events/{event_id}/resolve', 'POST', 'FAIL', 400, status_code, str(response))

    def test_notification_endpoints(self):
        """Test Notification endpoints (3 endpoints)"""
        print("\n🔔 Testing Notification Endpoints...")

        # GET /notifications
        status_code, response = self.make_request('GET', '/notifications')
        if status_code == 200:
            self.log_test('/notifications', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/notifications', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /notifications/test
        status_code, response = self.make_request('POST', '/notifications/test', {})
        if status_code == 200:
            self.log_test('/notifications/test', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/notifications/test', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /notifications/{notification_id}/read (expect validation error)
        status_code, response = self.make_request('POST', '/notifications/invalid_id/read', {})
        if status_code in [400, 404, 422]:
            self.log_test('/notifications/{notification_id}/read', 'POST', 'PASS', 400, status_code, 'Expected validation error')
        else:
            self.log_test('/notifications/{notification_id}/read', 'POST', 'FAIL', 400, status_code, str(response))

    def test_queue_endpoints(self):
        """Test Queue endpoints (8 endpoints)"""
        print("\n⚡ Testing Queue Endpoints...")

        # GET /queue/status
        status_code, response = self.make_request('GET', '/queue/status')
        if status_code == 200:
            self.log_test('/queue/status', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/queue/status', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /queue/stats
        status_code, response = self.make_request('GET', '/queue/stats')
        if status_code == 200:
            self.log_test('/queue/stats', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/queue/stats', 'GET', 'FAIL', 200, status_code, str(response))

        # GET /queue/failed-jobs
        status_code, response = self.make_request('GET', '/queue/failed-jobs')
        if status_code == 200:
            self.log_test('/queue/failed-jobs', 'GET', 'PASS', 200, status_code)
        else:
            self.log_test('/queue/failed-jobs', 'GET', 'FAIL', 200, status_code, str(response))

        # POST /queue/test-email
        status_code, response = self.make_request('POST', '/queue/test-email', {})
        if status_code == 200:
            self.log_test('/queue/test-email', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/queue/test-email', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /queue/test-security-processing
        status_code, response = self.make_request('POST', '/queue/test-security-processing', {})
        if status_code == 200:
            self.log_test('/queue/test-security-processing', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/queue/test-security-processing', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /queue/test-cleanup
        status_code, response = self.make_request('POST', '/queue/test-cleanup', {})
        if status_code == 200:
            self.log_test('/queue/test-cleanup', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/queue/test-cleanup', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /queue/cleanup
        status_code, response = self.make_request('POST', '/queue/cleanup', {})
        if status_code == 200:
            self.log_test('/queue/cleanup', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/queue/cleanup', 'POST', 'FAIL', 200, status_code, str(response))

        # POST /queue/process-security-events
        status_code, response = self.make_request('POST', '/queue/process-security-events', {})
        if status_code == 200:
            self.log_test('/queue/process-security-events', 'POST', 'PASS', 200, status_code)
        else:
            self.log_test('/queue/process-security-events', 'POST', 'FAIL', 200, status_code, str(response))

    def run_all_tests(self):
        """Run all endpoint tests"""
        print("🚀 Starting Comprehensive Endpoint Testing...")
        print(f"📊 Total endpoints to test: 75")
        print(f"🎯 Target: 100% endpoint functionality")
        print("=" * 60)
        
        # Test basic connectivity first
        if not self.test_basic_endpoints():
            print("❌ Basic connectivity failed. Please ensure server is running on port 8001")
            return False
        
        # Setup
        if not self.setup_test_user():
            print("❌ Failed to setup test user. Aborting tests.")
            return False
        
        # Run test suites
        self.test_cors_endpoints()
        self.test_authentication_endpoints()
        self.test_two_factor_endpoints()
        self.test_oauth_endpoints()
        self.test_payment_endpoints()
        self.test_account_management_endpoints()
        self.test_otp_endpoints()
        self.test_security_endpoints()
        self.test_notification_endpoints()
        self.test_queue_endpoints()

        # Print summary
        self.print_summary()
        return True

    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.total_tests}")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        
        if self.total_tests > 0:
            success_rate = (self.passed_tests / self.total_tests) * 100
            print(f"📈 Success Rate: {success_rate:.1f}%")
            
            if success_rate >= 80:
                print("🎉 EXCELLENT: System is production ready!")
            elif success_rate >= 60:
                print("⚠️ GOOD: Minor issues need fixing")
            else:
                print("🚨 CRITICAL: Major issues need immediate attention")
        
        # Save detailed results
        with open('comprehensive_test_results.json', 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': self.total_tests,
                    'passed_tests': self.passed_tests,
                    'failed_tests': self.failed_tests,
                    'success_rate': (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0,
                    'test_date': datetime.now().isoformat()
                },
                'detailed_results': self.test_results
            }, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: comprehensive_test_results.json")

if __name__ == "__main__":
    tester = ComprehensiveEndpointTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
