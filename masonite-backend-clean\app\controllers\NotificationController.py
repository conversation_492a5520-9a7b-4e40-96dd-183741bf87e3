"""Notification Controller for managing notifications and notification settings"""

from masonite.controllers import <PERSON>
from masonite.request import Request
from masonite.response import Response
from masonite.validation import Validator
from masonite.authentication import Auth
from app.services.NotificationService import NotificationService


class NotificationController(Controller):
    """Handle notification operations and user notification preferences"""

    def __init__(self):
        """Initialize notification service"""
        self.notification_service = NotificationService()

    def get_user_notifications(self, request: Request, response: Response, auth: Auth):
        """
        GET /api/notifications
        Get notifications for current user
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Get user notifications
            notifications = self.notification_service.get_user_notifications(user.id)

            return response.json({
                'notifications': notifications,
                'total': len(notifications),
                'unread_count': len([n for n in notifications if not n.get('read', False)])
            }, 200)

        except Exception as e:
            print(f"❌ Get Notifications Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to load notifications'
                }
            }, 500)

    def mark_notification_read(self, request: Request, response: Response, validate: Validator, auth: Auth):
        """
        POST /api/notifications/{notification_id}/read
        Mark notification as read
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Get notification_id from route parameter
            notification_id = request.param('notification_id')

            # Validate notification_id
            if not notification_id or not notification_id.isdigit():
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'ValidationError',
                        'message': 'Invalid notification ID'
                    }
                }, 400)

            # Mark notification as read
            success = self.notification_service.mark_notification_as_read(notification_id, user.id)

            if success:
                return response.json({
                    'message': 'Notification marked as read'
                }, 200)
            else:
                return response.json({
                    'error': {
                        'statusCode': 404,
                        'name': 'NotFoundError',
                        'message': 'Notification not found'
                    }
                }, 404)

        except Exception as e:
            print(f"❌ Mark Notification Read Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to mark notification as read'
                }
            }, 500)

    def send_test_notification(self, request: Request, response: Response, validate: Validator, auth: Auth):
        """
        POST /api/notifications/test
        Send test notification (for testing purposes)
        """
        # Validate request (make type optional with default)
        errors = validate.validate({
            'type': 'string|in:security_alert,account_change,otp',
            'message': 'string'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            notification_type = request.input('type', 'security_alert')
            message = request.input('message', 'This is a test notification')

            # Send test notification based on type
            success = False
            if notification_type == 'security_alert':
                success = self.notification_service.send_security_alert(
                    user_id=user.id,
                    alert_type="Test Alert",
                    message=message,
                    severity="low"
                )
            elif notification_type == 'account_change':
                success = self.notification_service.send_account_change_notification(
                    user_id=user.id,
                    change_type="test_change",
                    description=message
                )
            elif notification_type == 'otp':
                success = self.notification_service.send_otp_notification(
                    user_id=user.id,
                    otp_code="123456",
                    otp_type="test"
                )

            if success:
                return response.json({
                    'message': f'Test {notification_type} notification sent successfully'
                }, 200)
            else:
                return response.json({
                    'error': {
                        'statusCode': 500,
                        'name': 'InternalServerError',
                        'message': 'Failed to send test notification'
                    }
                }, 500)

        except Exception as e:
            print(f"❌ Send Test Notification Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to send test notification'
                }
            }, 500)
