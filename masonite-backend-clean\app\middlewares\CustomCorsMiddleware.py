"""Custom CORS Middleware for handling Cross-Origin Resource Sharing"""

from masonite.middleware import Middleware
from masonite.request import Request
from masonite.response import Response


class CustomCorsMiddleware(Middleware):
    """Custom CORS middleware that actually works"""
    
    def before(self, request: Request, response: Response):
        """Handle CORS headers before request processing"""

        # Debug logging
        print(f"🔍 CORS Middleware BEFORE: {request.get_request_method()} {request.get_path()}")

        # Get origin from request
        origin = request.header('Origin')
        print(f"🔍 Origin: {origin}")

        # Set CORS headers
        response.header('Access-Control-Allow-Origin', '*')
        response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
        response.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, X-Requested-With, Accept')
        response.header('Access-Control-Allow-Credentials', 'true')
        response.header('Access-Control-Max-Age', '86400')

        print(f"🔍 CORS headers set in middleware")

        # Handle preflight OPTIONS requests
        if request.get_request_method().upper() == 'OPTIONS':
            print(f"🔍 Handling OPTIONS request")
            return response.json({}, 204)

        return request
    
    def after(self, request: Request, response: Response):
        """Ensure CORS headers are set after request processing"""

        print(f"🔍 CORS Middleware AFTER: {request.get_request_method()} {request.get_path()}")

        # Make sure CORS headers are always present
        if not response.get_header('Access-Control-Allow-Origin'):
            print(f"🔍 Setting CORS headers in AFTER method")
            response.header('Access-Control-Allow-Origin', '*')
            response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
            response.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, X-Requested-With, Accept')
            response.header('Access-Control-Allow-Credentials', 'true')
        else:
            print(f"🔍 CORS headers already set: {response.get_header('Access-Control-Allow-Origin')}")

        return request
