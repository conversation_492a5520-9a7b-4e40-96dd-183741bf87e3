[{"method": "POST", "endpoint": "/api/auth/signup", "has_cors": true, "result": {"status": 422, "cors_origin": "http://localhost:4200", "cors_methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS", "cors_headers": "Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token", "cors_credentials": "true", "has_cors": true}}, {"method": "POST", "endpoint": "/api/auth/login", "has_cors": true, "result": {"status": 401, "cors_origin": "http://localhost:4200", "cors_methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS", "cors_headers": "Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token", "cors_credentials": "true", "has_cors": true}}, {"method": "GET", "endpoint": "/api/auth/profile", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "GET", "endpoint": "/api/auth/me", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "POST", "endpoint": "/api/2fa/setup", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "GET", "endpoint": "/api/2fa/status", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "GET", "endpoint": "/api/auth/oauth/providers", "has_cors": true, "result": {"status": 200, "cors_origin": "http://localhost:4200", "cors_methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS", "cors_headers": "Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token", "cors_credentials": "true", "has_cors": true}}, {"method": "GET", "endpoint": "/api/auth/oauth/google/url", "has_cors": true, "result": {"status": 200, "cors_origin": "http://localhost:4200", "cors_methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS", "cors_headers": "Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token", "cors_credentials": "true", "has_cors": true}}, {"method": "GET", "endpoint": "/api/payments/test", "has_cors": true, "result": {"status": 200, "cors_origin": "http://localhost:4200", "cors_methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS", "cors_headers": "Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token", "cors_credentials": "true", "has_cors": true}}, {"method": "POST", "endpoint": "/api/payments/create-order", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "GET", "endpoint": "/api/payments/my-payments", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "POST", "endpoint": "/api/otp/send", "has_cors": true, "result": {"status": 200, "cors_origin": "http://localhost:4200", "cors_methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS", "cors_headers": "Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token", "cors_credentials": "true", "has_cors": true}}, {"method": "GET", "endpoint": "/api/otp/status", "has_cors": true, "result": {"status": 200, "cors_origin": "http://localhost:4200", "cors_methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS", "cors_headers": "Origin, Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token", "cors_credentials": "true", "has_cors": true}}, {"method": "GET", "endpoint": "/api/security/dashboard", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "GET", "endpoint": "/api/security/events", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "POST", "endpoint": "/api/account/request-deletion", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "GET", "endpoint": "/api/account/deletion-status", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "GET", "endpoint": "/api/notifications", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "GET", "endpoint": "/api/queue/status", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}, {"method": "GET", "endpoint": "/api/queue/stats", "has_cors": false, "result": {"status": 401, "cors_origin": null, "cors_methods": null, "cors_headers": null, "cors_credentials": null, "has_cors": false}}]