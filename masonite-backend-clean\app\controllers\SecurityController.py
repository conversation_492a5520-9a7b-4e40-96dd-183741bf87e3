"""Security Controller for advanced security features and monitoring"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.validation import Validator
from masonite.authentication import Auth
from app.services.SecurityService import SecurityService
from app.models.SecurityEvent import SecurityEvent


class SecurityController(Controller):
    """Handle security monitoring, logging, and management operations"""

    def __init__(self):
        """Initialize security service"""
        self.security_service = SecurityService()

    def get_security_dashboard(self, request: Request, response: Response, auth: Auth):
        """
        GET /api/security/dashboard
        Get security dashboard data (admin only)
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Check if user has admin privileges (implement your own logic)
            # For now, we'll allow any authenticated user

            # Get dashboard data
            dashboard_data = self.security_service.get_security_dashboard_data(user.id)

            return response.json(dashboard_data, 200)

        except Exception as e:
            print(f"❌ Security Dashboard Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to load security dashboard'
                }
            }, 500)

    def get_user_security_events(self, request: Request, response: Response, auth: Auth):
        """
        GET /api/security/events/user
        Get security events for current user
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            print(f"🔍 Getting security events for user: {user.id}")

            # Get user's security events
            events = SecurityEvent.get_events_by_user(user.id, 50)
            print(f"🔍 Found {len(events)} events")
            
            formatted_events = []
            for i, event in enumerate(events):
                try:
                    print(f"🔍 Formatting event {i+1}: ID={event.id}, Type={event.event_type}")
                    formatted_event = self.security_service._format_event(event)
                    formatted_events.append(formatted_event)
                    print(f"✅ Event {i+1} formatted successfully")
                except Exception as format_error:
                    print(f"❌ Error formatting event {i+1}: {str(format_error)}")
                    # Skip problematic events instead of failing completely
                    continue

            return response.json({
                'events': formatted_events,
                'total': len(formatted_events)
            }, 200)

        except Exception as e:
            print(f"❌ User Security Events Error: {str(e)}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to load security events'
                }
            }, 500)

    def unlock_account(self, request: Request, response: Response, validate: Validator, auth: Auth):
        """
        POST /api/security/unlock-account
        Manually unlock a user account (admin only)
        """
        # Validate request
        errors = validate.validate({
            'user_id': 'required|string'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            # Get current user (admin)
            admin_user = request.user()
            if not admin_user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            user_id = request.input('user_id')
            
            # Validate user_id is not empty
            if not user_id or user_id.strip() == '':
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequestError',
                        'message': 'User ID cannot be empty'
                    }
                }, 400)

            # Unlock the account
            success = self.security_service.unlock_account(user_id, admin_user.id)

            if success:
                return response.json({
                    'message': f'Account {user_id} unlocked successfully'
                }, 200)
            else:
                return response.json({
                    'error': {
                        'statusCode': 404,
                        'name': 'NotFoundError',
                        'message': 'User not found or unlock failed'
                    }
                }, 404)

        except Exception as e:
            print(f"❌ Account Unlock Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to unlock account'
                }
            }, 500)

    def resolve_security_event(self, request: Request, response: Response, validate: Validator, auth: Auth):
        """
        POST /api/security/events/{event_id}/resolve
        Mark a security event as resolved (admin only)
        """
        try:
            # Get current user (admin)
            admin_user = request.user()
            if not admin_user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Get event_id from route parameter
            event_id = request.param('event_id')

            # Validate event_id
            if not event_id or not event_id.isdigit():
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'ValidationError',
                        'message': 'Invalid event ID'
                    }
                }, 400)

            # Find and resolve the event
            event = SecurityEvent.find(event_id)
            if not event:
                return response.json({
                    'error': {
                        'statusCode': 404,
                        'name': 'NotFoundError',
                        'message': 'Security event not found'
                    }
                }, 404)

            event.mark_resolved(admin_user.id)

            return response.json({
                'message': f'Security event {event_id} marked as resolved'
            }, 200)

        except Exception as e:
            print(f"❌ Resolve Security Event Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to resolve security event'
                }
            }, 500)

    def get_security_statistics(self, request: Request, response: Response, auth: Auth):
        """
        GET /api/security/statistics
        Get security statistics and metrics
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Get dashboard data which includes statistics
            dashboard_data = self.security_service.get_security_dashboard_data()

            return response.json({
                'statistics': dashboard_data.get('statistics', {}),
                'summary': {
                    'total_events': len(dashboard_data.get('recent_events', [])),
                    'unresolved_count': len(dashboard_data.get('unresolved_events', [])),
                    'user_events': len(dashboard_data.get('user_events', []))
                }
            }, 200)

        except Exception as e:
            print(f"❌ Security Statistics Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to load security statistics'
                }
            }, 500)

    def cleanup_security_data(self, request: Request, response: Response, auth: Auth):
        """
        POST /api/security/cleanup
        Clean up old security data (admin only)
        """
        try:
            # Get current user (admin)
            admin_user = request.user()
            if not admin_user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Perform cleanup
            result = self.security_service.cleanup_security_data()

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ Security Cleanup Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to cleanup security data'
                }
            }, 500)

    def check_account_status(self, request: Request, response: Response, validate: Validator, auth: Auth):
        """
        GET /api/security/account-status
        Check current user's account security status
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Check account status
            is_locked = self.security_service.is_account_locked(user.id)

            # Get recent security events for this user
            recent_events = SecurityEvent.get_events_by_user(user.id, 10)

            # Check for any unresolved security issues
            unresolved_events = SecurityEvent.where('user_id', str(user.id))\
                                           .where('resolved', False)\
                                           .where('requires_action', True)\
                                           .get()

            return response.json({
                'account_locked': is_locked,
                'login_attempts': getattr(user, 'login_attempts', 0),
                'last_login': str(getattr(user, 'last_login_at', None)),
                'recent_events_count': len(recent_events),
                'unresolved_issues': len(unresolved_events),
                'security_score': self._calculate_security_score(user, recent_events),
                'recommendations': self._get_security_recommendations(user, recent_events)
            }, 200)

        except Exception as e:
            print(f"❌ Account Status Check Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to check account status'
                }
            }, 500)

    def _calculate_security_score(self, user, recent_events):
        """Calculate a security score for the user (0-100)"""
        score = 100

        # Deduct points for recent failed logins
        failed_logins = len([e for e in recent_events if e.event_type == 'login_failed'])
        score -= failed_logins * 5

        # Deduct points if 2FA is not enabled
        if not getattr(user, 'two_factor_enabled', False):
            score -= 20

        # Deduct points if email is not verified
        if not getattr(user, 'email_verified_at', None):
            score -= 15

        # Deduct points for high-risk events
        high_risk_events = len([e for e in recent_events if e.risk_level in ['high', 'critical']])
        score -= high_risk_events * 10

        return max(0, min(100, score))

    def _get_security_recommendations(self, user, recent_events):
        """Get security recommendations for the user"""
        recommendations = []

        # Check 2FA
        if not getattr(user, 'two_factor_enabled', False):
            recommendations.append({
                'type': 'enable_2fa',
                'message': 'Enable Two-Factor Authentication for enhanced security',
                'priority': 'high'
            })

        # Check email verification
        if not getattr(user, 'email_verified_at', None):
            recommendations.append({
                'type': 'verify_email',
                'message': 'Verify your email address',
                'priority': 'medium'
            })

        # Check for recent failed logins
        failed_logins = len([e for e in recent_events if e.event_type == 'login_failed'])
        if failed_logins > 3:
            recommendations.append({
                'type': 'review_activity',
                'message': 'Review recent login activity for suspicious attempts',
                'priority': 'high'
            })

        # Check password age (if available)
        # This would require tracking password change dates

        return recommendations

    def get_suspicious_activity(self, request: Request, response: Response, auth: Auth):
        """
        GET /api/security/suspicious-activity
        Get suspicious activity for current user
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Get suspicious activities for this user
            suspicious_events = SecurityEvent.where('user_id', str(user.id))\
                                           .where('event_type', 'suspicious_activity')\
                                           .order_by('created_at', 'desc')\
                                           .limit(20)\
                                           .get()

            formatted_events = [self.security_service._format_event(event) for event in suspicious_events]

            return response.json({
                'suspicious_activities': formatted_events,
                'total': len(formatted_events)
            }, 200)

        except Exception as e:
            print(f"❌ Suspicious Activity Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to load suspicious activities'
                }
            }, 500)

    def get_security_analysis(self, request: Request, response: Response, auth: Auth):
        """
        GET /api/security/analysis
        Get security analysis for current user
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Get security analysis data
            analysis_data = self.security_service.get_security_analysis(user.id)

            return response.json(analysis_data, 200)

        except Exception as e:
            print(f"❌ Security Analysis Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to perform security analysis'
                }
            }, 500)
