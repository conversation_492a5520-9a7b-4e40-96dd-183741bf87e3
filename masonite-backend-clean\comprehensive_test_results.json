{"summary": {"total_tests": 67, "passed_tests": 65, "failed_tests": 2, "success_rate": 97.01492537313433, "test_date": "2025-06-16T05:22:48.078725"}, "detailed_results": [{"endpoint": "/cors/preflight", "method": "OPTIONS", "status": "PASS", "expected_code": 200, "actual_code": 204, "message": "", "timestamp": "2025-06-16T05:20:09.406657"}, {"endpoint": "/auth/login", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:14.943710"}, {"endpoint": "/auth/register", "method": "POST", "status": "PASS", "expected_code": 201, "actual_code": 201, "message": "", "timestamp": "2025-06-16T05:20:17.530793"}, {"endpoint": "/auth/profile", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:19.750868"}, {"endpoint": "/auth/refresh", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:22.135289"}, {"endpoint": "/auth/verify-email", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:20:24.352815"}, {"endpoint": "/auth/forgot-password", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:26.616480"}, {"endpoint": "/auth/reset-password", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 422, "message": "Expected validation error", "timestamp": "2025-06-16T05:20:28.680750"}, {"endpoint": "/auth/logout", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:30.978089"}, {"endpoint": "/two-factor/setup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:38.243936"}, {"endpoint": "/two-factor/verify", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:20:40.468041"}, {"endpoint": "/two-factor/recovery-codes", "method": "GET", "status": "FAIL", "expected_code": 200, "actual_code": 400, "message": "{'error': {'statusCode': 400, 'name': 'BadRequestError', 'message': 'Two-factor authentication is not enabled'}}", "timestamp": "2025-06-16T05:20:42.692093"}, {"endpoint": "/two-factor/regenerate-codes", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 422, "message": "Expected validation error", "timestamp": "2025-06-16T05:20:44.795581"}, {"endpoint": "/two-factor/disable", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 422, "message": "Expected validation error", "timestamp": "2025-06-16T05:20:46.921084"}, {"endpoint": "/oauth/providers", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:48.976225"}, {"endpoint": "/oauth/google/url", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:51.040377"}, {"endpoint": "/oauth/github/url", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:53.109075"}, {"endpoint": "/oauth/exchange-token", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:20:55.285772"}, {"endpoint": "/oauth/callback", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 400, "message": "Expected response", "timestamp": "2025-06-16T05:20:57.326302"}, {"endpoint": "/payments/test", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:20:59.359400"}, {"endpoint": "/payments/create-order", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:01.667192"}, {"endpoint": "/payments/verify", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:21:03.844905"}, {"endpoint": "/payments/user", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:06.292585"}, {"endpoint": "/payments/analytics", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:08.569190"}, {"endpoint": "/payments/refunds", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:10.902677"}, {"endpoint": "/payments/refund", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 404, "message": "Expected validation error", "timestamp": "2025-06-16T05:21:13.083755"}, {"endpoint": "/payments/cancel", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 404, "message": "Expected validation error", "timestamp": "2025-06-16T05:21:15.298235"}, {"endpoint": "/payments/status/{order_id}", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:17.471776"}, {"endpoint": "/payments/webhook", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:21:19.515845"}, {"endpoint": "/account/request-deletion", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:22.018080"}, {"endpoint": "/account/deletion-status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:24.408196"}, {"endpoint": "/account/cancel-deletion", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:26.833866"}, {"endpoint": "/account/export-data", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:29.058610"}, {"endpoint": "/account/request-export", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:31.533946"}, {"endpoint": "/account/cleanup-expired", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:33.634001"}, {"endpoint": "/account/confirm-deletion", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:21:35.752130"}, {"endpoint": "/account/check-preserved-data", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:37.865874"}, {"endpoint": "/account/restore-data", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:21:39.992250"}, {"endpoint": "/account/delete-preserved-data", "method": "DELETE", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:42.272357"}, {"endpoint": "/otp/send", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:44.722064"}, {"endpoint": "/otp/send-email", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:47.268248"}, {"endpoint": "/otp/send-sms", "method": "POST", "status": "FAIL", "expected_code": 200, "actual_code": 500, "message": "{'error': {'statusCode': 500, 'name': 'InternalServerError', 'message': 'Phone number not registered. Please register first or use email.'}}", "timestamp": "2025-06-16T05:21:49.376007"}, {"endpoint": "/otp/verify", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "Expected response", "timestamp": "2025-06-16T05:21:51.755006"}, {"endpoint": "/otp/login", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 401, "message": "Expected error response", "timestamp": "2025-06-16T05:21:54.139544"}, {"endpoint": "/otp/status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:56.381626"}, {"endpoint": "/otp/cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:21:58.536669"}, {"endpoint": "/security/dashboard", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:01.070167"}, {"endpoint": "/security/events", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:03.376068"}, {"endpoint": "/security/events/user", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:05.663082"}, {"endpoint": "/security/suspicious-activity", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:07.945913"}, {"endpoint": "/security/analysis", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:10.222251"}, {"endpoint": "/security/statistics", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:12.933778"}, {"endpoint": "/security/account-status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:15.580263"}, {"endpoint": "/security/unlock-account", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:22:17.692387"}, {"endpoint": "/security/cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:19.971505"}, {"endpoint": "/security/events/{event_id}/resolve", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:22:22.177573"}, {"endpoint": "/notifications", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:24.494129"}, {"endpoint": "/notifications/test", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:27.123010"}, {"endpoint": "/notifications/{notification_id}/read", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T05:22:29.227460"}, {"endpoint": "/queue/status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:31.406682"}, {"endpoint": "/queue/stats", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:33.583632"}, {"endpoint": "/queue/failed-jobs", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:35.888441"}, {"endpoint": "/queue/test-email", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:38.170315"}, {"endpoint": "/queue/test-security-processing", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:40.508610"}, {"endpoint": "/queue/test-cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:42.999748"}, {"endpoint": "/queue/cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:45.759409"}, {"endpoint": "/queue/process-security-events", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T05:22:48.077722"}]}